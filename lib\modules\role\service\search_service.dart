import 'dart:async';

import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/modules/role/service/search_state_manager.dart';

/// 搜索服务
/// 

class SearchService {
  // 搜索仓库
  final SearchRepository _repository = Get.find<SearchRepository>();

  // 搜索状态管理器
  final SearchStateManager _stateManager = SearchStateManager();

  // 搜索防抖Worker - 使用GetX
  Worker? _debounceWorker;

  // 当前搜索请求的取消状态 - 使用GetX响应式变量
  final RxBool _isSearchCancelled = false.obs;

  // 防抖时间(毫秒) - 优化为500ms，平衡响应速度和请求频率
  static const int _debounceTime = 500;

  // 对外暴露的状态访问器
  SearchStateManager get stateManager => _stateManager;
  
  /// 搜索角色
  ///
  /// 根据关键词和过滤条件搜索角色
  /// [keyword] 搜索关键词
  /// [page] 页码
  /// [pageSize] 每页大小
  /// [forceRefresh] 是否强制刷新
  /// 返回搜索结果及分页信息
  Future<Map<String, dynamic>> searchRoles({
    required String keyword,
    int page = 1,
    int pageSize = 20,
    bool forceRefresh = false,
  }) async {
    // 取消之前的搜索请求
    _cancelCurrentSearch();

    // 重置取消状态
    _isSearchCancelled.value = false;

    try {
      LogUtil.debug('搜索角色服务: 关键词=$keyword, 页码=$page');
      
      // 设置搜索状态
      _stateManager.setSearchState(SearchState.searching);

      // 检查是否已被取消
      if (_isSearchCancelled.value) {
        LogUtil.debug('搜索请求已被取消');
        _stateManager.setSearchState(SearchState.idle);
        return _getEmptyResult(page, pageSize);
      }



      // 调用仓库搜索角色（Repository层已处理重试机制）
      final result = await _repository.searchRoles(
        keyword: keyword,
        page: page,
        size: pageSize,
        forceRefresh: forceRefresh,
      );

      // 再次检查是否已被取消
      if (_isSearchCancelled.value) {
        LogUtil.debug('搜索请求在完成前被取消');
        _stateManager.setSearchState(SearchState.idle);
        return _getEmptyResult(page, pageSize);
      }


      
      // 添加搜索记录到历史
      await _stateManager.addSearchRecord(keyword);
      
      _stateManager.setSearchState(SearchState.completed);
      return result;
    } catch (e) {
      // 检查是否是因为取消导致的错误
      if (_isSearchCancelled.value) {
        LogUtil.debug('搜索请求被取消: $e');
        _stateManager.setSearchState(SearchState.idle);
        return _getEmptyResult(page, pageSize);
      }

      LogUtil.error('搜索角色服务错误: $e');
      _stateManager.setSearchState(SearchState.error);
      
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '搜索角色失败，请检查网络连接并稍后再试',
        showSnackbar: true,
      );
      
      return _getEmptyResult(page, pageSize);
    }
  }

  /// 获取空的搜索结果
  Map<String, dynamic> _getEmptyResult(int page, int pageSize) {
    return {
      'items': <AiRole>[],
      'total': 0,
      'page': page,
      'size': pageSize,
      'pages': 0
    };
  }

  /// 取消当前搜索请求 - 使用GetX响应式变量
  void _cancelCurrentSearch() {
    if (!_isSearchCancelled.value) {
      _isSearchCancelled.value = true;
      LogUtil.debug('已取消当前搜索请求');
    }
  }
  
  /// 获取搜索建议(带防抖)
  ///
  /// 根据输入的关键词返回匹配的角色名称建议列表
  /// [keyword] 搜索关键词
  /// [limit] 匹配数量限制
  /// [callback] 回调函数，用于接收搜索建议
  void getSearchSuggestions(
    String keyword, {
    int limit = 10,
    required Function(List<String>) callback
  }) {
    // 取消上一次的Worker
    _debounceWorker?.dispose();

    // 如果关键词为空，直接返回空列表
    if (keyword.trim().isEmpty) {
      _stateManager.setSearchState(SearchState.idle);
      callback([]);
      return;
    }

    // 设置搜索状态
    _stateManager.setSearchState(SearchState.loading);

    // 创建防抖触发器和GetX debounce Worker
    final searchTrigger = keyword.obs;
    _debounceWorker = debounce(
      searchTrigger,
      (value) async {
        try {
          // 再次检查关键词是否仍然有效（用户可能已经清空或修改）
          if (value.trim().isEmpty) {
            _stateManager.setSearchState(SearchState.idle);
            callback([]);
            return;
          }

          // 从仓库获取搜索建议（Repository层已处理请求去重）
          final suggestions = await _repository.getSearchSuggestions(value, limit: limit);

          // 检查是否是最新的搜索请求（防止过期请求覆盖新结果）
          if (searchTrigger.value == value) {
            _stateManager.setSearchState(SearchState.completed);
            callback(suggestions);
          }
        } catch (e) {
          LogUtil.error('获取搜索建议服务错误: $e');

          // 只有当前请求才更新错误状态
          if (searchTrigger.value == value) {
            _stateManager.setSearchState(SearchState.error);

            // 使用ErrorHandler处理异常，但不显示snackbar，避免干扰用户输入
            ErrorHandler.handleException(
              e,
              message: '获取搜索建议失败',
              showSnackbar: false,
            );

            callback([]);
          }
        }
      },
      time: const Duration(milliseconds: _debounceTime),
    );

    // 触发搜索
    searchTrigger.value = keyword;
  }
  
  /// 获取随机角色名
  /// 
  /// 用于搜索框的默认提示
  Future<String> getRandomRoleName() async {
    try {
      return await _repository.getRandomRoleName();
    } catch (e) {
      LogUtil.error('获取随机角色名服务错误: $e');
      // 使用ErrorHandler处理异常，但不显示snackbar，因为这是非关键功能
      ErrorHandler.handleException(
        e,
        message: '获取随机角色名失败',
        showSnackbar: false,
      );
      return '';
    }
  }
  
  /// 获取搜索历史
  ///
  /// 返回用户的搜索历史记录
  Future<List<SearchRecord>> getSearchHistory({
    int page = 1,
    int size = 10,
    bool forceRefresh = false,
  }) async {
    try {
      // 直接从Repository获取搜索历史（Repository层处理缓存）
      final result = await _repository.getSearchHistory(
        page: page,
        size: size,
        forceRefresh: forceRefresh,
      );

      if (result.containsKey('items') && result['items'] is List) {
        final serverHistory = List<SearchRecord>.from(result['items']);
        LogUtil.debug('搜索历史服务: 获取到${serverHistory.length}条记录');
        return serverHistory;
      }

      return [];
    } catch (e) {
      LogUtil.error('获取搜索历史服务错误: $e');

      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '获取搜索历史失败',
        showSnackbar: true,
      );
      return [];
    }
  }
  
  /// 删除搜索记录
  ///
  /// 删除指定的搜索记录
  Future<bool> deleteSearchRecord(int recordId) async {
    try {
      // Repository层会处理缓存清理
      return await _repository.deleteSearchRecord(recordId);
    } catch (e) {
      LogUtil.error('删除搜索记录服务错误: $e');
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '删除搜索记录失败',
        showSnackbar: true,
      );
      return false;
    }
  }
  

  
  /// 删除所有搜索记录
  ///
  /// 通过获取所有搜索历史并批量删除实现
  Future<int> deleteAllSearchRecords() async {
    try {
      // Repository层会处理缓存清理
      final deleteResult = await _repository.deleteAllSearchRecords();
      return deleteResult['deleted_count'] ?? 0;
    } catch (e) {
      LogUtil.error('删除所有搜索记录服务错误: $e');
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '删除所有搜索记录失败',
        showSnackbar: true,
      );
      return 0;
    }
  }
  
  /// 加载搜索的下一页
  /// 
  /// 根据当前搜索条件加载下一页结果
  /// [currentResult] 当前搜索结果
  /// [keyword] 搜索关键词
  /// 返回新的搜索结果
  Future<Map<String, dynamic>> loadNextPage(
    Map<String, dynamic> currentResult, {
    required String keyword,
  }) async {
    try {
      final int currentPage = currentResult['page'] ?? 1;
      final int totalPages = currentResult['pages'] ?? 1;
      final int pageSize = currentResult['size'] ?? 20;
      
      // 检查是否有下一页
      if (currentPage >= totalPages) {
        LogUtil.debug('没有更多搜索结果了');
        return currentResult;
      }
      
      // 加载下一页
      final nextPage = currentPage + 1;
      LogUtil.debug('加载搜索结果下一页: $nextPage');
      
      final result = await searchRoles(
        keyword: keyword,
        page: nextPage,
        pageSize: pageSize,
      );
      
      // 合并结果
      if (result['items'] is List<AiRole> && currentResult['items'] is List<AiRole>) {
        final List<AiRole> combinedItems = [
          ...(currentResult['items'] as List<AiRole>),
          ...(result['items'] as List<AiRole>),
        ];
        
        return {
          ...result,
          'items': combinedItems,
        };
      }
      
      return result;
    } catch (e) {
      LogUtil.error('加载搜索下一页错误: $e');
      // 使用ErrorHandler处理异常
      ErrorHandler.handleException(
        e,
        message: '加载更多结果失败',
        showSnackbar: true,
      );
      return currentResult;
    }
  }
  
  /// 取消防抖Worker和当前搜索请求
  void cancelDebounce() {
    _debounceWorker?.dispose();
    _debounceWorker = null;
    _cancelCurrentSearch();

    // 重置搜索建议状态
    _stateManager.setSearchState(SearchState.idle);
    LogUtil.debug('SearchService: 已取消搜索建议防抖和当前搜索请求');
  }

  /// 清理搜索状态
  void clearSearchState() {
    _stateManager.reset();
    cancelDebounce();
  }

  /// 获取当前搜索状态（响应式访问）
  bool get isCurrentlySearching => _stateManager.searchState.value == SearchState.searching || 
                                   _stateManager.searchState.value == SearchState.loading;
  
  /// 清除搜索缓存
  ///
  /// [type] 缓存类型：'history', 'all'
  Future<bool> clearCache(String type) => _stateManager.clearCache(type);
  

}