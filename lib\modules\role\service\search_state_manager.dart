import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';

/// 搜索状态枚举
enum SearchState {
  idle,         // 空闲状态
  searching,    // 搜索中
  loading,      // 加载中
  completed,    // 完成
  error,        // 错误
}


class SearchStateManager {
  // 搜索状态
  final Rx<SearchState> _searchState = SearchState.idle.obs;

  // 对外暴露的状态访问器
  Rx<SearchState> get searchState => _searchState;
  
  /// 设置搜索状态
  void setSearchState(SearchState state) {
    _searchState.value = state;
    LogUtil.debug('SearchStateManager: 搜索状态变更为 - ${state.toString()}');
  }
  
  /// 重置状态管理器
  void reset() {
    _searchState.value = SearchState.idle;
    LogUtil.debug('SearchStateManager: 状态管理器已重置');
  }

}