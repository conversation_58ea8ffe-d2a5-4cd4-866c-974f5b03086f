import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/constants/cache_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';

/// 搜索状态枚举
enum SearchState {
  idle,         // 空闲状态
  searching,    // 搜索中
  loading,      // 加载中
  completed,    // 完成
  error,        // 错误
}


class SearchStateManager {
  // 缓存管理器
  final CacheManager _cacheManager = Get.find<CacheManager>();
  
  // 对外暴露缓存管理器（用于特殊清理操作）
  CacheManager get cacheManager => _cacheManager;
  
  // 搜索状态
  final Rx<SearchState> _searchState = SearchState.idle.obs;
  
  // 搜索历史本地缓存
  final RxList<SearchRecord> _localSearchHistory = <SearchRecord>[].obs;

  // 对外暴露的状态访问器
  Rx<SearchState> get searchState => _searchState;
  RxList<SearchRecord> get localSearchHistory => _localSearchHistory;
  

  

  
  /// 获取本地搜索历史缓存
  Future<List<SearchRecord>> getLocalSearchHistory() async {
    try {
      // 先返回内存中的历史记录
      if (_localSearchHistory.isNotEmpty) {
        LogUtil.debug('SearchStateManager: 搜索历史内存缓存命中');
        return _localSearchHistory.toList();
      }

      // 从持久化缓存中加载
      final cachedHistory = await _cacheManager.get<List<SearchRecord>>(
        CacheConstants.searchHistoryCacheKey,
        strategy: CacheStrategy.persistentOnly,
        maxAge: CacheConstants.getExpiryForModule('search', type: 'history'),
        fromJson: (json) => (json['history'] as List<dynamic>)
            .map((item) => SearchRecord.fromJson(item as Map<String, dynamic>))
            .toList(),
      );

      if (cachedHistory != null) {
        // 同步到内存缓存
        _localSearchHistory.assignAll(cachedHistory);
        LogUtil.debug('SearchStateManager: 搜索历史持久化缓存命中，加载${cachedHistory.length}条记录');
        return cachedHistory;
      }

      LogUtil.debug('SearchStateManager: 搜索历史缓存未命中');
      return [];
    } catch (e) {
      LogUtil.error('SearchStateManager: 获取搜索历史缓存失败 - $e');
      return [];
    }
  }
  
  /// 设置本地搜索历史缓存
  /// 
  /// [history] 搜索历史列表
  Future<bool> setLocalSearchHistory(List<SearchRecord> history) async {
    try {
      final success = await _cacheManager.set(
        CacheConstants.searchHistoryCacheKey,
        {'history': history.map((record) => record.toJson()).toList()},
        strategy: CacheStrategy.both,
        expiry: CacheConstants.getExpiryForModule('search', type: 'history'),
        toJson: (data) => data,
      );
      
      if (success) {
        // 同步到内存缓存
        _localSearchHistory.assignAll(history);
        LogUtil.debug('SearchStateManager: 搜索历史缓存设置成功，共${history.length}条记录');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('SearchStateManager: 设置搜索历史缓存失败 - $e');
      return false;
    }
  }
  
  /// 添加搜索记录到本地历史
  /// 
  /// [keyword] 搜索关键词
  Future<void> addSearchRecord(String keyword) async {
    try {
      if (keyword.trim().isEmpty) return;
      
      final now = DateTime.now();
      final normalizedKeyword = keyword.trim();
      
      // 移除重复的搜索记录
      _localSearchHistory.removeWhere((record) => record.keyword == normalizedKeyword);
      
      // 添加新记录到开头
      final newRecord = SearchRecord(
        id: now.millisecondsSinceEpoch,
        keyword: normalizedKeyword,
        searchCount: 1,
        lastSearchAt: now,
      );
      
      _localSearchHistory.insert(0, newRecord);
      
      // 限制历史记录数量（最多保留50条）
      if (_localSearchHistory.length > 50) {
        _localSearchHistory.removeRange(50, _localSearchHistory.length);
      }
      
      // 异步保存到持久化缓存
      await setLocalSearchHistory(_localSearchHistory.toList());
      
      LogUtil.debug('SearchStateManager: 添加搜索记录 - $normalizedKeyword');
    } catch (e) {
      LogUtil.error('SearchStateManager: 添加搜索记录失败 - $e');
    }
  }
  
  /// 清除搜索缓存
  ///
  /// [type] 缓存类型：'history', 'all'
  Future<bool> clearCache(String type) async {
    try {
      bool success = true;

      switch (type) {
        case 'history':
          // 清除搜索历史缓存
          success = await _cacheManager.remove(CacheConstants.searchHistoryCacheKey);
          _localSearchHistory.clear();
          LogUtil.debug('SearchStateManager: 搜索历史缓存已清除');
          break;

        case 'all':
          // 清除所有搜索相关缓存
          success = await clearCache('history');
          LogUtil.debug('SearchStateManager: 所有搜索缓存已清除');
          break;

        default:
          LogUtil.warn('SearchStateManager: 未知的缓存类型 - $type');
          success = false;
      }

      return success;
    } catch (e) {
      LogUtil.error('SearchStateManager: 清除搜索缓存失败 - $e');
      return false;
    }
  }
  
  /// 设置搜索状态
  void setSearchState(SearchState state) {
    _searchState.value = state;
    LogUtil.debug('SearchStateManager: 搜索状态变更为 - ${state.toString()}');
  }
  
  /// 重置状态管理器
  void reset() {
    _searchState.value = SearchState.idle;
    _localSearchHistory.clear();
    LogUtil.debug('SearchStateManager: 状态管理器已重置');
  }

}